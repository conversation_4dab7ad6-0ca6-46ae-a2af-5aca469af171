diff --git a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
index d88d004..c11fa4b 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
@@ -727,7 +727,7 @@ function tokenizeTrail(effects, ok, nok) {
       code === codes.colon ||
       code === codes.semicolon ||
       code === codes.questionMark ||
-      code === codes.underscore ||
+      // code === codes.underscore ||
       code === codes.tilde
     ) {
       effects.consume(code)
diff --git a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
index 651d637..e0b6e54 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
@@ -636,7 +636,7 @@ function tokenizePath(effects, ok) {
       code === 60 ||
       code === 63 ||
       code === 93 ||
-      code === 95 ||
+      // code === 95 ||
       code === 126
     ) {
       return effects.check(trail, ok, pathAtPunctuation)(code)
